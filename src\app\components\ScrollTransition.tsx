'use client';

import { useEffect, useRef, useState } from 'react';
import { motion, useScroll, useTransform, useSpring } from 'framer-motion';

interface ScrollTransitionProps {
  children: React.ReactNode;
  nextSection: React.ReactNode;
  className?: string;
}

export default function ScrollTransition({
  children,
  nextSection,
  className = ""
}: ScrollTransitionProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });

  // Smooth spring animation for the transition
  const smoothProgress = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
    restDelta: 0.001
  });

  // Transform values for the "falling through space" effect
  const scale = useTransform(smoothProgress, [0, 0.2, 0.5, 0.8, 1], [1, 1.1, 1.5, 2.5, 4]);
  const opacity = useTransform(smoothProgress, [0, 0.2, 0.6, 0.9, 1], [1, 1, 0.8, 0.3, 0]);
  const rotateX = useTransform(smoothProgress, [0, 0.3, 0.7, 1], [0, 5, 25, 60]);
  const rotateY = useTransform(smoothProgress, [0, 0.4, 0.8, 1], [0, 8, 20, 45]);
  const z = useTransform(smoothProgress, [0, 0.2, 0.5, 0.8, 1], [0, 20, 100, 300, 800]);

  // Background transition effect - more dramatic
  const backgroundOpacity = useTransform(smoothProgress, [0, 0.3, 0.7, 1], [0, 0.2, 0.6, 1]);

  // Next section reveal - smoother transition
  const nextSectionY = useTransform(smoothProgress, [0, 0.4, 0.8, 1], [200, 100, 20, 0]);
  const nextSectionOpacity = useTransform(smoothProgress, [0, 0.4, 0.8, 1], [0, 0.3, 0.8, 1]);

  // Additional space effects
  const blur = useTransform(smoothProgress, [0, 0.3, 0.7, 1], [0, 2, 8, 15]);
  const brightness = useTransform(smoothProgress, [0, 0.5, 1], [1, 1.2, 0.3]);

  useEffect(() => {
    const unsubscribe = smoothProgress.on('change', (latest) => {
      setIsTransitioning(latest > 0.2 && latest < 0.8);
    });

    return () => unsubscribe();
  }, [smoothProgress]);

  return (
    <div ref={containerRef} className={`relative scroll-transition-container ${className}`}>
      {/* Main content with transition effects */}
      <motion.div
        style={{
          scale,
          opacity,
          rotateX,
          rotateY,
          z,
          filter: `blur(${blur}px) brightness(${brightness})`,
        }}
        className="relative z-10 transform-gpu space-transition"
      >
        {children}
      </motion.div>

      {/* Transition background overlay */}
      <motion.div
        style={{ opacity: backgroundOpacity }}
        className="fixed inset-0 bg-gradient-to-b from-black via-[#1a1a2e] to-[#16213e] z-5 pointer-events-none"
      />

      {/* Stars/space effect during transition */}
      {isTransitioning && (
        <div className="fixed inset-0 z-5 pointer-events-none">
          {[...Array(100)].map((_, i) => {
            const size = Math.random() * 3 + 1;
            const speed = Math.random() * 3 + 1;
            return (
              <motion.div
                key={i}
                className="absolute bg-white rounded-full"
                style={{
                  width: `${size}px`,
                  height: `${size}px`,
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  boxShadow: `0 0 ${size * 2}px rgba(255, 255, 255, 0.8)`,
                }}
                animate={{
                  opacity: [0, 1, 0],
                  scale: [0, 1, 0],
                  y: [0, -(typeof window !== 'undefined' ? window.innerHeight : 800) * speed],
                }}
                transition={{
                  duration: 3 / speed,
                  repeat: Infinity,
                  delay: Math.random() * 3,
                  ease: "linear",
                }}
              />
            );
          })}

          {/* Nebula-like background effects */}
          {[...Array(5)].map((_, i) => (
            <motion.div
              key={`nebula-${i}`}
              className="absolute rounded-full"
              style={{
                width: `${Math.random() * 200 + 100}px`,
                height: `${Math.random() * 200 + 100}px`,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                background: `radial-gradient(circle, rgba(102, 102, 255, 0.1) 0%, rgba(0, 68, 204, 0.05) 50%, transparent 100%)`,
              }}
              animate={{
                opacity: [0.1, 0.3, 0.1],
                scale: [0.8, 1.2, 0.8],
                rotate: [0, 360],
              }}
              transition={{
                duration: 8 + Math.random() * 4,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>
      )}

      {/* Next section preview */}
      <motion.div
        style={{
          y: nextSectionY,
          opacity: nextSectionOpacity,
        }}
        className="relative z-20"
      >
        {nextSection}
      </motion.div>
    </div>
  );
}
