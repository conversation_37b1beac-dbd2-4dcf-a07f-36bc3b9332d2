'use client';

import { Suspense, useRef, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { useGLTF, Environment } from '@react-three/drei';
import * as THREE from 'three';

interface BackgroundModelProps {
  modelPath: string;
  scale?: number;
  position?: [number, number, number];
  scrollOffset?: number;
}

function BackgroundModel({ modelPath, scale = 1, position = [0, 0, 0], scrollOffset = 0 }: BackgroundModelProps) {
  const { scene } = useGLTF(modelPath);
  const modelRef = useRef<THREE.Group>(null);

  useFrame((state) => {
    if (modelRef.current) {
      // Slow auto-rotation
      modelRef.current.rotation.y += 0.002;

      // Subtle parallax effect based on scroll
      modelRef.current.position.x = position[0] + scrollOffset * 0.1;
      modelRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 0.5) * 0.2;

      // Gentle breathing scale animation
      const breathe = 1 + Math.sin(state.clock.elapsedTime * 0.3) * 0.05;
      modelRef.current.scale.setScalar(scale * breathe);

      // Subtle mouse-following effect
      const mouse = state.mouse;
      modelRef.current.rotation.x = mouse.y * 0.1;
      modelRef.current.rotation.z = mouse.x * 0.05;
    }
  });

  return (
    <group ref={modelRef} position={position}>
      <primitive object={scene.clone()} />
    </group>
  );
}

interface Background3DProps {
  modelPath: string;
  className?: string;
  scale?: number;
  position?: [number, number, number];
  opacity?: number;
  scrollOffset?: number;
}

export default function Background3D({
  modelPath,
  className = "absolute inset-0 w-full h-full",
  scale = 1,
  position = [0, 0, 0],
  opacity = 0.3,
  scrollOffset = 0
}: Background3DProps) {
  const canvasRef = useRef<HTMLDivElement>(null);

  return (
    <div
      ref={canvasRef}
      className={`${className} bg-3d-integration`}
      style={{
        opacity,
        pointerEvents: 'none',
        zIndex: 1
      }}
    >
      <Canvas
        camera={{
          position: [0, 0, 8],
          fov: 45,
          near: 0.1,
          far: 1000
        }}
        style={{ background: 'transparent' }}
        dpr={[1, 2]}
        performance={{ min: 0.5 }}
      >
        {/* Subtle lighting for background */}
        <ambientLight intensity={0.2} />
        <directionalLight
          position={[5, 5, 5]}
          intensity={0.3}
          castShadow={false}
        />

        {/* Environment for subtle reflections */}
        <Environment preset="dawn" environmentIntensity={0.1} />

        {/* Background Model */}
        <Suspense fallback={null}>
          <BackgroundModel
            modelPath={modelPath}
            scale={scale}
            position={position}
            scrollOffset={scrollOffset}
          />
        </Suspense>
      </Canvas>
    </div>
  );
}

// Preload function
export function preloadBackgroundModel(modelPath: string) {
  useGLTF.preload(modelPath);
}
