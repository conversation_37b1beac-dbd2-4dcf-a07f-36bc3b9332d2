'use client';

import { Suspense, useRef, useEffect, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { useGLTF, Environment, Text, OrbitControls } from '@react-three/drei';
import * as THREE from 'three';

interface SkillData {
  name: string;
  level: number;
  icon: React.ReactNode;
  position: [number, number, number];
  color: string;
}

interface GlitchTextProps {
  text: string;
  position: [number, number, number];
  color: string;
  isVisible: boolean;
  delay: number;
}

function GlitchText({ text, position, color, isVisible, delay }: GlitchTextProps) {
  const [displayText, setDisplayText] = useState('');
  const [isGlitching, setIsGlitching] = useState(false);
  const textRef = useRef<any>(null);

  const glitchChars = '!@#$%^&*()_+-=[]{}|;:,.<>?~`';

  useEffect(() => {
    if (!isVisible) return;

    const timeout = setTimeout(() => {
      setIsGlitching(true);
      let currentIndex = 0;

      const glitchInterval = setInterval(() => {
        if (currentIndex < text.length) {
          // Glitch effect for current character
          const glitchText = text.split('').map((char, index) => {
            if (index < currentIndex) return char;
            if (index === currentIndex) {
              return Math.random() > 0.7 ? char : glitchChars[Math.floor(Math.random() * glitchChars.length)];
            }
            return glitchChars[Math.floor(Math.random() * glitchChars.length)];
          }).join('');

          setDisplayText(glitchText);

          // Settle the current character after some glitching
          if (Math.random() > 0.3) {
            currentIndex++;
          }
        } else {
          setDisplayText(text);
          setIsGlitching(false);
          clearInterval(glitchInterval);
        }
      }, 50);

      return () => clearInterval(glitchInterval);
    }, delay);

    return () => clearTimeout(timeout);
  }, [isVisible, text, delay]);

  useFrame((state) => {
    if (textRef.current && isVisible) {
      // Floating animation
      textRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2 + delay) * 0.1;

      // Subtle glow effect - check if material exists and has emissive property
      if (textRef.current.material && textRef.current.material.emissive) {
        if (isGlitching) {
          textRef.current.material.emissive.setHex(0x444444);
        } else {
          textRef.current.material.emissive.setHex(0x000000);
        }
      }
    }
  });

  if (!isVisible) return null;

  return (
    <Text
      ref={textRef}
      position={position}
      fontSize={0.3}
      color={color}
      anchorX="center"
      anchorY="middle"
      outlineWidth={0.02}
      outlineColor="#000000"
      material-toneMapped={false}
    >
      {displayText}
    </Text>
  );
}

interface Interactive3DModelProps {
  modelPath: string;
  skills: SkillData[];
}

function Interactive3DModel({ modelPath, skills }: Interactive3DModelProps) {
  const gltf = useGLTF(modelPath);
  const modelRef = useRef<THREE.Group>(null);
  const [skillsVisible, setSkillsVisible] = useState(false);

  // Safety check for GLTF loading
  if (!gltf || !gltf.scene) {
    return null;
  }

  const { scene } = gltf;

  useFrame((state) => {
    if (modelRef.current) {
      // Slow rotation
      modelRef.current.rotation.y += 0.005;

      // Breathing animation
      const breathe = 1 + Math.sin(state.clock.elapsedTime * 0.8) * 0.05;
      modelRef.current.scale.setScalar(breathe);

      // Mouse interaction - add safety checks
      if (state.mouse) {
        modelRef.current.rotation.x = state.mouse.y * 0.2;
        modelRef.current.rotation.z = state.mouse.x * 0.1;
      }
    }
  });

  useEffect(() => {
    const timer = setTimeout(() => {
      setSkillsVisible(true);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <group>
      {/* Main 3D Model - Centered and Prominent */}
      <group ref={modelRef} position={[0, 0, 0]} scale={3}>
        <primitive object={scene.clone()} />
      </group>

      {/* Floating Skill Labels */}
      {skills.map((skill, index) => (
        <group key={skill.name}>
          <GlitchText
            text={skill.name.toUpperCase()}
            position={skill.position}
            color={skill.color}
            isVisible={skillsVisible}
            delay={index * 500}
          />

          {/* Skill Level Bar */}
          {skillsVisible && (
            <mesh position={[skill.position[0], skill.position[1] - 0.4, skill.position[2]]}>
              <boxGeometry args={[1, 0.1, 0.02]} />
              <meshBasicMaterial color="#333333" transparent opacity={0.8} />
            </mesh>
          )}

          {/* Skill Level Fill */}
          {skillsVisible && (
            <mesh position={[skill.position[0] - 0.5 + (skill.level / 100) * 0.5, skill.position[1] - 0.4, skill.position[2] + 0.01]}>
              <boxGeometry args={[(skill.level / 100), 0.1, 0.02]} />
              <meshBasicMaterial color={skill.color} />
            </mesh>
          )}
        </group>
      ))}

      {/* Orbital Rings */}
      <group>
        {[2, 3, 4].map((radius, index) => (
          <mesh key={index} rotation={[Math.PI / 2, 0, 0]} position={[0, -1, 0]}>
            <ringGeometry args={[radius - 0.05, radius, 64]} />
            <meshBasicMaterial
              color="#6666ff"
              transparent
              opacity={0.1 - index * 0.02}
              side={THREE.DoubleSide}
            />
          </mesh>
        ))}
      </group>
    </group>
  );
}

interface Interactive3DSkillsProps {
  modelPath: string;
  skills: SkillData[];
  className?: string;
}

export default function Interactive3DSkills({
  modelPath,
  skills,
  className = "w-full h-full"
}: Interactive3DSkillsProps) {
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    // Reset error state when props change
    setHasError(false);
  }, [modelPath, skills]);

  if (hasError) {
    return (
      <div className={`${className} flex items-center justify-center`}>
        <div className="text-white/60 text-center">
          <p>3D Model Loading Error</p>
          <button
            onClick={() => setHasError(false)}
            className="mt-2 px-4 py-2 bg-[#6666ff] text-white rounded"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <Canvas
        camera={{ position: [0, 2, 6], fov: 60 }}
        style={{ background: 'transparent' }}
        dpr={[1, 2]}
        onError={() => setHasError(true)}
      >
        {/* Dramatic Gaming-style Lighting */}
        <ambientLight intensity={0.4} />
        <directionalLight position={[10, 10, 5]} intensity={1.2} color="#6666ff" />
        <pointLight position={[-10, -10, -10]} intensity={0.8} color="#66ff99" />
        <spotLight
          position={[0, 10, 0]}
          angle={0.4}
          penumbra={1}
          intensity={1.5}
          color="#ffffff"
          target-position={[0, 0, 0]}
        />

        {/* Environment */}
        <Environment preset="night" environmentIntensity={0.3} />

        {/* Interactive Controls */}
        <OrbitControls
          enablePan={false}
          enableZoom={true}
          enableRotate={true}
          autoRotate={false}
          minDistance={4}
          maxDistance={12}
          minPolarAngle={Math.PI / 6}
          maxPolarAngle={Math.PI - Math.PI / 6}
        />

        {/* Interactive 3D Model with Skills */}
        <Suspense fallback={
          <mesh position={[0, 0, 0]}>
            <boxGeometry args={[1, 1, 1]} />
            <meshBasicMaterial color="#6666ff" wireframe />
          </mesh>
        }>
          <Interactive3DModel modelPath={modelPath} skills={skills} />
        </Suspense>
      </Canvas>
    </div>
  );
}

// Preload function
export function preloadInteractive3D(modelPath: string) {
  useGLTF.preload(modelPath);
}
