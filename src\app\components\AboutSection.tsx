'use client';

import { useState, useEffect } from 'react';
import { FaPython, FaRobot, FaBrain, FaChartLine, FaCode, FaDatabase } from 'react-icons/fa';
import dynamic from 'next/dynamic';

// Dynamically import the interactive 3D component
const Interactive3DSkills = dynamic(() => import('./Interactive3DSkills'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-96 flex items-center justify-center">
      <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-[#6666ff]"></div>
    </div>
  )
});

// Preload the model for better performance
if (typeof window !== 'undefined') {
  import('./Interactive3DSkills').then(({ preloadInteractive3D }) => {
    preloadInteractive3D('/a_windy_day.glb');
  });
}

export default function AboutSection() {
  const [expanded, setExpanded] = useState(false);
  const [scrollOffset, setScrollOffset] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const aboutSection = document.getElementById('about');
      if (aboutSection) {
        const rect = aboutSection.getBoundingClientRect();
        const offset = (window.innerHeight - rect.top) / window.innerHeight;
        setScrollOffset(offset);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Gaming-style skills data with 3D positioning
  const skills3D = [
    {
      name: 'Python',
      level: 95,
      icon: <FaPython className="text-4xl text-blue-500" />,
      position: [-3, 2, 1] as [number, number, number],
      color: '#3776ab'
    },
    {
      name: 'TensorFlow',
      level: 90,
      icon: <FaRobot className="text-4xl text-orange-500" />,
      position: [3, 1.5, -1] as [number, number, number],
      color: '#ff6f00'
    },
    {
      name: 'PyTorch',
      level: 85,
      icon: <FaBrain className="text-4xl text-red-500" />,
      position: [-2.5, -0.5, 2] as [number, number, number],
      color: '#ee4c2c'
    },
    {
      name: 'Scikit-Learn',
      level: 80,
      icon: <FaChartLine className="text-4xl text-green-500" />,
      position: [2.8, -1, 1.5] as [number, number, number],
      color: '#f7931e'
    },
    {
      name: 'Deep Learning',
      level: 88,
      icon: <FaBrain className="text-4xl text-indigo-500" />,
      position: [0, 2.5, -2] as [number, number, number],
      color: '#3f51b5'
    },
    {
      name: 'SQL/NoSQL',
      level: 85,
      icon: <FaDatabase className="text-4xl text-yellow-500" />,
      position: [-3.5, 0, -1] as [number, number, number],
      color: '#ffc107'
    }
  ];

  return (
    <section id="about" className="py-20 relative min-h-screen">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-[#6666ff] to-[#0044cc] bg-clip-text text-transparent">
            <span className="sound-wave-indicator"></span>
            <span className="sound-wave-indicator"></span>
            <span className="sound-wave-indicator"></span>
            NEURAL INTERFACE
            <span className="sound-wave-indicator"></span>
            <span className="sound-wave-indicator"></span>
            <span className="sound-wave-indicator"></span>
          </h2>
          <p className="text-lg text-black mt-4 font-semibold">AI/ML Engineer • Skills Matrix Loading...</p>
        </div>

        {/* Gaming-style layout */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 relative z-10">
          {/* Bio Panel */}
          <div className="lg:col-span-1">
            <div className="gaming-ui-container p-6 h-full">
              <h3 className="text-xl font-bold bg-gradient-to-r from-[#6666ff] to-[#0044cc] bg-clip-text text-transparent mb-4">OPERATOR PROFILE</h3>
              <div className="space-y-4 text-black">
                <div className="skill-stat-box">
                  <span className="text-[#0044cc] font-semibold">STATUS:</span> ACTIVE
                </div>
                <div className="skill-stat-box">
                  <span className="text-[#0044cc] font-semibold">EXPERIENCE:</span> 5+ YEARS
                </div>
                <div className="skill-stat-box">
                  <span className="text-[#0044cc] font-semibold">SPECIALIZATION:</span> AI/ML SYSTEMS
                </div>
                <div className="skill-stat-box">
                  <span className="text-[#0044cc] font-semibold">CLEARANCE:</span> NEURAL NETWORKS
                </div>
              </div>

              {expanded ? (
                <div className="mt-6 space-y-4">
                  <div className="skill-stat-box">
                    <span className="text-[#0044cc] font-semibold">EDUCATION:</span> MS Computer Science
                  </div>
                  <div className="skill-stat-box">
                    <span className="text-[#0044cc] font-semibold">RESEARCH:</span> Transformer Architectures
                  </div>
                  <div className="skill-stat-box">
                    <span className="text-[#0044cc] font-semibold">IMPACT:</span> 40% Efficiency Boost
                  </div>
                  <button
                    onClick={() => setExpanded(false)}
                    className="skill-stat-box hover:bg-[#6666ff]/20 cursor-pointer transition-all duration-300"
                  >
                    <span className="text-[#ff6666]">MINIMIZE DATA</span>
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => setExpanded(true)}
                  className="mt-6 skill-stat-box hover:bg-[#6666ff]/20 cursor-pointer transition-all duration-300 w-full"
                >
                  <span className="text-[#0044cc] font-semibold">EXPAND PROFILE</span>
                </button>
              )}
            </div>
          </div>

          {/* 3D Interactive Skills Display */}
          <div className="lg:col-span-2">
            <div className="gaming-ui-container h-96 lg:h-[600px] relative overflow-hidden">
              <div className="absolute top-4 left-4 z-20">
                <h3 className="text-lg font-bold bg-gradient-to-r from-[#6666ff] to-[#0044cc] bg-clip-text text-transparent">SKILLS MATRIX</h3>
                <p className="text-sm text-black font-medium">Interactive 3D Neural Network</p>
              </div>

              <Interactive3DSkills
                modelPath="/a_windy_day.glb"
                skills={skills3D}
                className="w-full h-full"
              />

              <div className="absolute bottom-4 right-4 z-20">
                <div className="text-xs text-black">
                  <p>MOUSE: ROTATE • SCROLL: ZOOM</p>
                  <p>STATUS: <span className="text-[#0044cc] font-semibold">ONLINE</span></p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}